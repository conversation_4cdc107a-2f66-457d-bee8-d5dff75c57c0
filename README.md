# E-Commerce Web Application

Dự án E-commerce với cấu trúc Frontend (Client + Admin) và Backend riêng biệt.

## Cấu trúc dự án

```
E-web/
├── FE/                     # Frontend Applications
│   ├── client/            # Ứng dụng khách hàng
│   │   ├── src/
│   │   │   ├── components/    # Components tái sử dụng
│   │   │   ├── pages/        # Các trang chính
│   │   │   ├── hooks/        # Custom hooks
│   │   │   ├── services/     # API calls
│   │   │   ├── store/        # State management
│   │   │   ├── utils/        # Utilities
│   │   │   └── styles/       # CSS/SCSS files
│   │   ├── public/
│   │   └── package.json
│   │
│   ├── admin/             # Ứng dụng quản trị
│   │   ├── src/
│   │   │   ├── components/    # Admin components
│   │   │   ├── pages/        # Admin pages
│   │   │   ├── hooks/        # Admin hooks
│   │   │   ├── services/     # Admin API calls
│   │   │   ├── store/        # Admin state
│   │   │   ├── utils/        # Admin utilities
│   │   │   └── styles/       # Admin styles
│   │   ├── public/
│   │   └── package.j<PERSON>
│   │
│   └── shared/            # Code dùng chung
│       ├── components/    # Shared components
│       ├── hooks/         # Shared hooks
│       ├── utils/         # Shared utilities
│       └── types/         # TypeScript types
│
├── BE/                    # Backend API
│   ├── controllers/       # Route controllers
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── config/           # Configuration files
│   ├── helpers/          # Helper functions
│   ├── validate/         # Validation schemas
│   └── package.json
│
└── README.md
```

## Công nghệ sử dụng

### Frontend
- **React 19** - UI Library
- **React Router DOM** - Routing
- **Axios** - HTTP Client
- **Vite** - Build tool

### Backend
- **Node.js** - Runtime
- **Express.js** - Web framework
- **MongoDB/MySQL** - Database (tùy chọn)

## Cài đặt và chạy dự án

### Backend
```bash
cd BE
npm install
npm run dev
```

### Frontend Client
```bash
cd FE/client
npm install
npm run dev
```

### Frontend Admin
```bash
cd FE/admin
npm install
npm run dev
```

## API Endpoints (Cơ bản)

### Authentication
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/register` - Đăng ký
- `POST /api/auth/logout` - Đăng xuất

### Products
- `GET /api/products` - Lấy danh sách sản phẩm
- `GET /api/products/:id` - Lấy chi tiết sản phẩm
- `POST /api/products` - Tạo sản phẩm mới (Admin)
- `PUT /api/products/:id` - Cập nhật sản phẩm (Admin)
- `DELETE /api/products/:id` - Xóa sản phẩm (Admin)

### Orders
- `GET /api/orders` - Lấy danh sách đơn hàng
- `POST /api/orders` - Tạo đơn hàng mới
- `GET /api/orders/:id` - Lấy chi tiết đơn hàng
- `PUT /api/orders/:id` - Cập nhật đơn hàng

### Users (Admin)
- `GET /api/users` - Lấy danh sách người dùng
- `GET /api/users/:id` - Lấy thông tin người dùng
- `PUT /api/users/:id` - Cập nhật thông tin người dùng
- `DELETE /api/users/:id` - Xóa người dùng