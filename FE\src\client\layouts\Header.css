.header {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.header-logo .logo-link {
  text-decoration: none;
  color: #333;
}

.header-logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

/* Navigation */
.header-nav {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover {
  color: #007bff;
}

/* Search */
.header-search {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 8px 15px;
  min-width: 300px;
}

.search-input {
  border: none;
  background: transparent;
  outline: none;
  flex: 1;
  padding: 5px;
  font-size: 14px;
}

.search-btn {
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 5px;
}

/* Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.cart-link {
  text-decoration: none;
  position: relative;
}

.cart-icon {
  font-size: 24px;
  position: relative;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* User Menu */
.user-dropdown {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.user-btn:hover {
  background-color: #f8f9fa;
}

.user-avatar {
  font-size: 20px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  min-width: 150px;
  z-index: 1001;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  text-decoration: none;
  color: #333;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 10px;
}

.auth-btn {
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s;
}

.login-btn {
  color: #007bff;
  border: 1px solid #007bff;
}

.login-btn:hover {
  background-color: #007bff;
  color: white;
}

.register-btn {
  background-color: #007bff;
  color: white;
  border: 1px solid #007bff;
}

.register-btn:hover {
  background-color: #0056b3;
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.hamburger {
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: 0 15px;
  }
  
  .header-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #ddd;
  }
  
  .nav-open {
    display: block;
  }
  
  .nav-list {
    flex-direction: column;
    padding: 20px;
    gap: 15px;
  }
  
  .header-search {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .auth-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  .auth-btn {
    padding: 6px 12px;
    font-size: 14px;
  }
}
