.products-page {
  min-height: 100vh;
  padding: 40px 0;
}

.products-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 40px;
  margin-top: 30px;
}

/* Filters Sidebar */
.filters-sidebar {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.filter-section {
  margin-bottom: 30px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.search-input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.3s;
}

.filter-option:hover {
  color: #007bff;
}

.filter-option input[type="radio"] {
  margin: 0;
}

/* Products Main */
.products-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.products-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.results-info {
  font-weight: 500;
  color: #666;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-options label {
  font-weight: 500;
  color: #666;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  padding: 30px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.out-of-stock-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn, .wishlist-btn {
  background: white;
  color: #333;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-view-btn:hover {
  background: #007bff;
  color: white;
}

.wishlist-btn {
  padding: 10px;
  font-size: 16px;
}

.wishlist-btn:hover {
  background: #dc3545;
  color: white;
}

.product-info {
  padding: 20px;
}

.product-category {
  font-size: 12px;
  color: #007bff;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.4;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.stars {
  color: #ffc107;
  font-size: 14px;
}

.rating-text {
  color: #666;
  font-size: 12px;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 15px;
}

.product-actions {
  display: flex;
  gap: 10px;
}

.add-to-cart-btn {
  flex: 1;
  background: #007bff;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-to-cart-btn:hover:not(.disabled) {
  background: #0056b3;
}

.add-to-cart-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.view-detail-btn {
  background: transparent;
  color: #007bff;
  border: 1px solid #007bff;
  padding: 10px 15px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
  text-align: center;
}

.view-detail-btn:hover {
  background: #007bff;
  color: white;
}

.no-products {
  text-align: center;
  padding: 60px 30px;
  color: #666;
}

.no-products p {
  font-size: 16px;
  margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .products-layout {
    grid-template-columns: 250px 1fr;
    gap: 30px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
    padding: 25px;
  }
}

@media (max-width: 768px) {
  .products-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .filters-sidebar {
    position: static;
    padding: 20px;
  }

  .products-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .sort-options {
    justify-content: space-between;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .products-page {
    padding: 20px 0;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }

  .product-actions {
    flex-direction: column;
  }

  .filter-section {
    margin-bottom: 20px;
  }
}
